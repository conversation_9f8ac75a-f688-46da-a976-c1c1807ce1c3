<!DOCTYPE html><html lang="zh-CN"><head>
<meta charset="utf-8">
<meta content="width=device-width, initial-scale=1.0" name="viewport">
<title>目录</title>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
<script src="https://picture-search.tiangong.cn/tailwindcss.com"></script>
<style>
    body {
        width: 1280px;
        min-height: 720px;
        margin: 0;
        padding: 0;
    }
    .slide {
        width: 1280px;
        min-height: 720px;
        position: relative;
    }
    .gradient-bg {
        background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
    }
    .content-card {
        backdrop-filter: blur(8px);
        transition: all 0.3s ease;
        border-left: 6px solid transparent;
    }
    .content-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px -5px rgba(0, 0, 0, 0.15);
    }
    .card-1 { border-left-color: #FF6600; }
    .card-2 { border-left-color: #00CC66; }
    .card-3 { border-left-color: #3B82F6; }
    .card-4 { border-left-color: #9333EA; }
    .card-5 { border-left-color: #EF4444; }
    
    .number-badge {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
        color: white;
        margin-right: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .circle-decoration {
        border-radius: 50%;
        position: absolute;
        opacity: 0.15;
        filter: blur(3px);
    }
    .page-indicator {
        position: relative;
        display: inline-block;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 500;
    }
</style>
</head>
<body>
<div class="slide gradient-bg p-8 flex flex-col relative overflow-hidden">
    <!-- Decorative elements -->
    <div class="circle-decoration bg-gray-400" style="width: 300px; height: 300px; top: -100px; right: -50px;"></div>
    <div class="circle-decoration bg-gray-400" style="width: 200px; height: 200px; bottom: -50px; left: 100px;"></div>
    <div class="circle-decoration bg-gray-400" style="width: 150px; height: 150px; top: 300px; right: 200px;"></div>
    
    <!-- Header -->
    <!-- 当前位置: top=0px, 剩余空间: 720px -->
    <div class="mb-6 z-10">
        <h1 class="text-5xl font-bold text-gray-800 mb-2">目录</h1>
        <div class="h-1 w-32 bg-gradient-to-r from-orange-500 to-green-500 rounded-full"></div>
    </div>
    
    <!-- Content Grid -->
    <!-- 当前位置: top=80px, 剩余空间: 640px -->
    <div class="grid grid-cols-3 gap-6 z-10 flex-grow">
        <!-- Section 1 -->
        <div class="content-card card-1 bg-white bg-opacity-90 rounded-xl p-6 text-gray-800">
            <div class="flex items-center mb-5">
                <div class="number-badge bg-orange-500">
                    1
                </div>
                <h2 class="text-3xl font-bold">行业痛点分析</h2>
            </div>
            <ul class="space-y-2 ml-2">
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 行业物资管理痛点分析</span>
                    <span class="page-indicator bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第3页</span>
                </li>
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 氧化铝行业物资管理痛点</span>
                    <span class="page-indicator bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第4页</span>
                </li>
            </ul>
        </div>
        
        <!-- Section 2 -->
        <div class="content-card card-2 bg-white bg-opacity-90 rounded-xl p-6 text-gray-800">
            <div class="flex items-center mb-5">
                <div class="number-badge bg-green-500">
                    2
                </div>
                <h2 class="text-3xl font-bold">现有解决方案分析</h2>
            </div>
            <ul class="space-y-2 ml-2">
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 现有解决方案分析</span>
                    <span class="page-indicator bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第5页</span>
                </li>
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 传统ERP系统及局限性</span>
                    <span class="page-indicator bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第6页</span>
                </li>
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 智能仓储方案评估</span>
                    <span class="page-indicator bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第7页</span>
                </li>
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 供应链协同平台与移动应用</span>
                    <span class="page-indicator bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第8页</span>
                </li>
            </ul>
        </div>
        
        <!-- Section 3 -->
        <div class="content-card card-3 bg-white bg-opacity-90 rounded-xl p-6 text-gray-800">
            <div class="flex items-center mb-5">
                <div class="number-badge bg-blue-500">
                    3
                </div>
                <h2 class="text-3xl font-bold">功能模块设计</h2>
            </div>
            <ul class="space-y-2 ml-2">
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 采购管理模块</span>
                    <span class="page-indicator bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第10页</span>
                </li>
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 出入库管理模块</span>
                    <span class="page-indicator bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第11页</span>
                </li>
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 盘点管理模块</span>
                    <span class="page-indicator bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第12页</span>
                </li>
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 基础管理模块</span>
                    <span class="page-indicator bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第13页</span>
                </li>
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 统计预警模块</span>
                    <span class="page-indicator bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第14页</span>
                </li>
            </ul>
        </div>
        
        <!-- Section 4 -->
        <div class="content-card card-4 bg-white bg-opacity-90 rounded-xl p-6 text-gray-800 col-span-2">
            <div class="flex items-center mb-5">
                <div class="number-badge bg-purple-600">
                    4
                </div>
                <h2 class="text-3xl font-bold">软硬件方案</h2>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <ul class="space-y-2 ml-2">
                    <li class="flex justify-between items-center py-2">
                        <span class="text-lg">• 软件特色</span>
                        <span class="page-indicator bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-semibold">第15页</span>
                    </li>
                    <li class="flex justify-between items-center py-2">
                        <span class="text-lg">• 线上审批流程</span>
                        <span class="page-indicator bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-semibold">第16页</span>
                    </li>
                    <li class="flex justify-between items-center py-2">
                        <span class="text-lg">• 扫码出入库功能</span>
                        <span class="page-indicator bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-semibold">第17页</span>
                    </li>
                </ul>
                <ul class="space-y-2 ml-2">
                    <li class="flex justify-between items-center py-2">
                        <span class="text-lg">• 硬件方案对比</span>
                        <span class="page-indicator bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-semibold">第18页</span>
                    </li>
                    <li class="flex justify-between items-center py-2">
                        <span class="text-lg">• RFID标签方案</span>
                        <span class="page-indicator bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-semibold">第19页</span>
                    </li>
                    <li class="flex justify-between items-center py-2">
                        <span class="text-lg">• 二维码标签方案</span>
                        <span class="page-indicator bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-semibold">第20页</span>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Section 5 -->
        <div class="content-card card-5 bg-white bg-opacity-90 rounded-xl p-6 text-gray-800">
            <div class="flex items-center mb-5">
                <div class="number-badge bg-red-500">
                    5
                </div>
                <h2 class="text-3xl font-bold">客户案例</h2>
            </div>
            <ul class="space-y-2 ml-2">
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 客户案例：山西气田企业物资管理</span>
                    <span class="page-indicator bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第21页</span>
                </li>
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 解决方案实施过程</span>
                    <span class="page-indicator bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第22页</span>
                </li>
                <li class="flex justify-between items-center py-2">
                    <span class="text-lg">• 项目效果评估</span>
                    <span class="page-indicator bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第23页</span>
                </li>
            </ul>
        </div>
    </div>
    
    <!-- Footer -->
    <!-- 当前位置: top=680px, 剩余空间: 40px -->
    <div class="absolute bottom-10 right-10 text-gray-600 text-sm bg-white bg-opacity-80 px-4 py-1 rounded-full">
        <p>第 2 页 / 共 23 页</p>
    </div>
</div>

</body></html>