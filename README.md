# PPT样式集使用指南

这是一个基于Font Awesome 6.0.0-beta3的PPT样式集，包含了丰富的图标和样式，帮助您快速创建专业的PPT页面。

## 文件说明

- `base.css` - Font Awesome 6.0.0-beta3完整样式库
- `01目录.html` - 目录页面示例
- `02痛点分.html` - 内容页面示例

## 快速开始

### 1. 基础HTML结构

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>您的PPT标题</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://picture-search.tiangong.cn/tailwindcss.com"></script>
</head>
<body>
    <div class="slide gradient-bg p-8 flex flex-col relative overflow-hidden">
        <!-- 您的内容 -->
    </div>
</body>
</html>
```

### 2. 推荐的PPT尺寸

```css
body {
    width: 1280px;
    min-height: 720px;
    margin: 0;
    padding: 0;
}

.slide {
    width: 1280px;
    min-height: 720px;
    position: relative;
}
```

## 常用样式组件

### 背景渐变

```css
.gradient-bg {
    background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
}
```

### 数字徽章

```css
.number-badge {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: white;
    margin-right: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

### 内容卡片

```css
.content-card {
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
    border-left: 6px solid transparent;
}

.content-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px -5px rgba(0, 0, 0, 0.15);
}
```

## Font Awesome图标使用

### 常用图标类名

- `fas fa-chart-line` - 图表线条
- `fas fa-triangle-exclamation` - 警告三角
- `fas fa-cubes` - 立方体
- `fas fa-microchip` - 芯片
- `fas fa-building` - 建筑
- `fas fa-user` - 用户
- `fas fa-cog` - 设置
- `fas fa-star` - 星星
- `fas fa-heart` - 心形
- `fas fa-home` - 首页

### 图标使用示例

```html
<!-- 基础图标 -->
<i class="fas fa-chart-line"></i>

<!-- 带颜色的图标 -->
<i class="fas fa-chart-line text-blue-500"></i>

<!-- 大小调整 -->
<i class="fas fa-chart-line text-xl"></i>
<i class="fas fa-chart-line text-2xl"></i>
<i class="fas fa-chart-line text-3xl"></i>

<!-- 圆形背景图标 -->
<div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
    <i class="fas fa-chart-line text-white"></i>
</div>
```

## 颜色主题

### 推荐配色方案

- **橙色主题**: `bg-orange-500` (#FF6600)
- **绿色主题**: `bg-green-500` (#00CC66)
- **蓝色主题**: `bg-blue-500` (#3B82F6)
- **紫色主题**: `bg-purple-600` (#9333EA)
- **红色主题**: `bg-red-500` (#EF4444)

### 使用示例

```html
<!-- 数字徽章 -->
<div class="number-badge bg-orange-500">1</div>
<div class="number-badge bg-green-500">2</div>
<div class="number-badge bg-blue-500">3</div>

<!-- 页面指示器 -->
<span class="page-indicator bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold">第3页</span>
```

## 布局建议

### 目录页面布局

```html
<div class="grid grid-cols-3 gap-6 z-10 flex-grow">
    <!-- 3列网格布局 -->
    <div class="content-card bg-white bg-opacity-90 rounded-xl p-6">
        <!-- 内容 -->
    </div>
</div>
```

### 标题区域

```html
<div class="mb-6 z-10">
    <h1 class="text-5xl font-bold text-gray-800 mb-2">标题</h1>
    <div class="h-1 w-32 bg-gradient-to-r from-orange-500 to-green-500 rounded-full"></div>
</div>
```

### 页脚

```html
<div class="absolute bottom-10 right-10 text-gray-600 text-sm bg-white bg-opacity-80 px-4 py-1 rounded-full">
    <p>第 X 页 / 共 Y 页</p>
</div>
```

## 装饰元素

### 圆形装饰

```html
<div class="circle-decoration bg-gray-400" style="width: 300px; height: 300px; top: -100px; right: -50px;"></div>
```

```css
.circle-decoration {
    border-radius: 50%;
    position: absolute;
    opacity: 0.15;
    filter: blur(3px);
}
```

## 最佳实践

1. **保持一致性**: 在整个PPT中使用相同的颜色主题和字体大小
2. **合理间距**: 使用Tailwind CSS的间距类（p-6, m-4等）保持统一间距
3. **响应式设计**: 虽然PPT是固定尺寸，但保持良好的布局结构
4. **图标选择**: 选择语义明确的图标，避免过于复杂的图标
5. **颜色对比**: 确保文字和背景有足够的对比度

## 示例页面

参考项目中的示例文件：
- `01目录.html` - 展示了如何创建带数字编号的目录页面
- `02痛点分.html` - 展示了内容页面的布局和样式

## 技术栈

- **Font Awesome 6.0.0-beta3**: 图标库
- **Tailwind CSS**: 样式框架
- **HTML5**: 页面结构
- **CSS3**: 自定义样式和动画

---

通过使用这个样式集，您可以快速创建专业、美观的PPT页面。如有问题，请参考示例文件或Font Awesome官方文档。